"""
Agent Router with RAG Prioritization Logic
Intelligent query routing system that prioritizes RAG for document-related queries
with fallback to normal LLM when documents are irrelevant
"""

import os
import logging
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# Import core components
from rag_system import RAGSystem
from document_analyzer import DocumentA<PERSON>yzer
from web_search_tool import WebSearchTool

load_dotenv()

class AgentRouter:
    """
    Intelligent agent router implementing RAG-first prioritization for document queries
    """
    
    def __init__(self):
        """Initialize router with all processing tools"""
        self.rag_system = RAGSystem()
        self.web_search = WebSearchTool()
        self.doc_analyzer = DocumentAnalyzer(self.rag_system)
        self.logger = logging.getLogger(__name__)
    
    def route_query(self, query: str, context: Dict, uploaded_document: Optional[Dict] = None) -> str:
        """
        Route query to appropriate tool based on classification with RAG prioritization
        
        Args:
            query: User's question
            context: Session context including conversation history
            uploaded_document: Optional document data with relevance assessment
            
        Returns:
            Generated response using appropriate processing approach
        """
        try:
            # Special handling for document-related queries with RAG prioritization
            if uploaded_document and self.is_document_query(query):
                self.logger.info("Processing document-related query with RAG prioritization")
                return self.handle_document_query(query, context, uploaded_document)
            
            # Standard query routing for non-document queries
            query_type = self.classify_query(query)
            self.logger.info(f"Query classified as: {query_type}")
            
            if query_type == "rag_domain":
                return self.rag_system.query(query, context)
            elif query_type == "web_search":
                return self.web_search.search(query)
            elif query_type == "document_analysis":
                return self.doc_analyzer.analyze_query_without_document(query, context)
            else:
                return self.handle_general_query(query, context)
                
        except Exception as e:
            self.logger.error(f"Error in query routing: {str(e)}")
            return f"I encountered an error processing your query. Please try again."
    
    def is_document_query(self, query: str) -> bool:
        """
        Enhanced document query detection for RAG prioritization
        
        Args:
            query: User's question
            
        Returns:
            True if query is asking about the uploaded document
        """
        document_indicators = [
            'this document', 'this file', 'this pdf', 'this invoice', 'this bill',
            'uploaded document', 'attached file', 'in this', 'from this',
            'analyze this', 'what is this', 'explain this', 'summarize this',
            'calculate from this', 'extract from this', 'details in this',
            'what does this show', 'according to this', 'based on this document',
            'in the document', 'document shows', 'file contains'
        ]
        
        query_lower = query.lower()
        return any(indicator in query_lower for indicator in document_indicators)
    
    def handle_document_query(self, query: str, context: Dict, uploaded_document: Dict) -> str:
        """
        Handle document-specific queries with RAG prioritization strategy
        
        Args:
            query: User's question about the document
            context: Session context
            uploaded_document: Document data with content and metadata
            
        Returns:
            Response using appropriate RAG prioritization tier
        """
        try:
            # Get document relevance score (should be calculated during document processing)
            relevance_score = uploaded_document.get('relevance', 0.0)
            document_content = uploaded_document.get('content', '')
            
            self.logger.info(f"Document relevance score: {relevance_score}")
            
            # Apply RAG prioritization strategy based on relevance
            if relevance_score > 0.7:
                # Tier 1: RAG Priority - High relevance to CA domain
                self.logger.info("Using RAG Priority approach (Tier 1)")
                return self.doc_analyzer.answer_with_rag_priority(query, document_content, context)
                
            elif relevance_score > 0.3:
                # Tier 2: Hybrid Approach - Medium relevance
                self.logger.info("Using Hybrid approach (Tier 2)")
                return self.doc_analyzer.answer_with_hybrid_approach(query, document_content, context)
                
            else:
                # Tier 3: LLM-Only Fallback - Low relevance
                self.logger.info("Using LLM-only fallback (Tier 3)")
                return self.doc_analyzer.answer_with_llm_only(query, document_content, context)
                
        except Exception as e:
            self.logger.error(f"Error handling document query: {str(e)}")
            return "I encountered an error analyzing the document. Please try again."
    
    def classify_query(self, query: str, has_document: bool = False, document_relevance: float = 0.0) -> str:
        """
        Classify query using keyword matching and context analysis
        
        Args:
            query: User's question
            has_document: Whether a document is uploaded
            document_relevance: Document relevance score (0-1)
            
        Returns:
            Query classification type
        """
        # Domain-specific keywords for RAG routing
        rag_keywords = [
            'tax', 'legal', 'compliance', 'gst', 'itr', 'company law',
            'audit', 'accounting', 'financial', 'business registration',
            'partnership', 'proprietorship', 'corporation', 'income tax',
            'deduction', 'exemption', 'tds', 'tcs', 'balance sheet',
            'profit loss', 'ca', 'chartered accountant', 'advisory'
        ]
        
        # Web search keywords
        web_keywords = [
            'current', 'latest', 'now', 'today', 'best', 'top',
            'recent', 'new', 'updated', 'trending', 'news',
            'who is', 'what happened', 'when did', 'breaking'
        ]
        
        query_lower = query.lower()
        
        # Check for web search indicators
        if any(keyword in query_lower for keyword in web_keywords):
            return "web_search"
        
        # Check for RAG domain keywords
        elif any(keyword in query_lower for keyword in rag_keywords):
            return "rag_domain"
        
        # Check for document analysis without specific document
        elif any(word in query_lower for word in ['analyze', 'review', 'check', 'examine']):
            return "document_analysis"
        
        else:
            return "general"
    
    def handle_general_query(self, query: str, context: Dict) -> str:
        """
        Handle general queries that don't fit specific categories
        
        Args:
            query: User's question
            context: Session context
            
        Returns:
            General response with CA context
        """
        try:
            # Try RAG first for general queries as well
            rag_response = self.rag_system.query(query, context)
            
            # If RAG provides a substantial response, use it
            if len(rag_response.strip()) > 50:
                return rag_response
            else:
                # Fallback to general CA assistant response
                return self._generate_general_response(query, context)
                
        except Exception as e:
            self.logger.error(f"Error handling general query: {str(e)}")
            return self._generate_general_response(query, context)
    
    def _generate_general_response(self, query: str, context: Dict) -> str:
        """
        Generate general response for queries outside specific domains
        
        Args:
            query: User's question
            context: Session context
            
        Returns:
            General helpful response
        """
        general_response = f"""I understand you're asking about: "{query}"

As a CA assistant, I specialize in tax, legal, and business matters. While I can try to help with general questions, I'm most effective with:

• Tax planning and compliance
• Legal and regulatory matters
• Business registration and structure
• Financial analysis and accounting
• Government schemes and procedures

Could you please provide more context or rephrase your question in relation to these areas? If you have any documents related to your query, feel free to upload them for more specific assistance."""

        # Add source attribution for general response
        source_info = "🤖 **Source**: AI Assistant (General Guidance)"
        return f"{general_response}\n\n{source_info}"


if __name__ == "__main__":
    # Simple test
    router = AgentRouter()
    print("Agent Router initialized successfully!")
    
    # Test query classification
    test_queries = [
        "What are the tax deductions for AY 2024-25?",
        "Who is the richest person now?",
        "Analyze this document"
    ]
    
    for query in test_queries:
        classification = router.classify_query(query)
        print(f"Query: {query} -> Classification: {classification}")
